#!/usr/bin/env node

/**
 * VerifID Backend Setup Script
 * 
 * This script helps new users set up the Node.js backend quickly by:
 * - Checking Node.js version
 * - Installing dependencies
 * - Creating environment file
 * - Checking MongoDB connection
 * - Providing next steps
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 VerifID Backend Setup');
console.log('========================\n');

// Check Node.js version
function checkNodeVersion() {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    console.log(`📋 Checking Node.js version: ${nodeVersion}`);
    
    if (majorVersion < 16) {
        console.error('❌ Node.js version 16 or higher is required!');
        console.log('   Please update Node.js: https://nodejs.org/');
        process.exit(1);
    }
    
    console.log('✅ Node.js version is compatible\n');
}

// Check if package.json exists
function checkPackageJson() {
    if (!fs.existsSync('package.json')) {
        console.error('❌ package.json not found!');
        console.log('   Make sure you\'re in the backend directory.');
        process.exit(1);
    }
    console.log('✅ package.json found\n');
}

// Install dependencies
function installDependencies() {
    console.log('📦 Installing dependencies...');
    
    try {
        // Check if npm is available
        execSync('npm --version', { stdio: 'ignore' });
        console.log('   Using npm to install packages...');
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ Dependencies installed successfully\n');
    } catch (error) {
        console.error('❌ Failed to install dependencies');
        console.log('   Please run: npm install');
        process.exit(1);
    }
}

// Generate JWT secret
function generateJWTSecret() {
    return crypto.randomBytes(32).toString('hex');
}

// Create environment file
function createEnvFile() {
    const envPath = '.env';
    
    if (fs.existsSync(envPath)) {
        console.log('⚠️  .env file already exists, skipping creation');
        console.log('   You can update it manually if needed\n');
        return;
    }
    
    const jwtSecret = generateJWTSecret();
    
    const envContent = `# VerifID Backend Environment Variables
# Generated by setup script

# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration (update with your MongoDB URI)
MONGODB_URI=mongodb://localhost:27017/verifid

# JWT Configuration
JWT_SECRET=${jwtSecret}
JWT_EXPIRES_IN=30d

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# Generated on: ${new Date().toISOString()}
`;
    
    try {
        fs.writeFileSync(envPath, envContent);
        console.log('✅ Created .env file with generated JWT secret');
        console.log('   📝 Please update MONGODB_URI if needed\n');
    } catch (error) {
        console.error('❌ Could not create .env file');
        console.log('   Please create it manually using .env.example\n');
    }
}

// Check MongoDB connection
function checkMongoConnection() {
    console.log('🔍 Checking MongoDB connection...');
    
    try {
        // Try to connect to local MongoDB
        const { MongoClient } = require('mongodb');
        const uri = 'mongodb://localhost:27017';
        
        MongoClient.connect(uri, { 
            serverSelectionTimeoutMS: 3000,
            connectTimeoutMS: 3000 
        })
        .then(client => {
            console.log('✅ MongoDB is running and accessible');
            client.close();
        })
        .catch(error => {
            console.log('⚠️  MongoDB connection failed:');
            console.log('   Local MongoDB may not be running');
            console.log('   Consider using MongoDB Atlas or start local MongoDB\n');
        });
        
    } catch (error) {
        console.log('⚠️  MongoDB driver not available yet');
        console.log('   This is normal during initial setup\n');
    }
}

// Test server startup
function testServerStartup() {
    console.log('🧪 Testing server startup...');
    
    try {
        // Try to start the server briefly to check for errors
        const testProcess = execSync('timeout 5s npm start 2>&1 || true', { 
            encoding: 'utf8',
            timeout: 6000 
        });
        
        if (testProcess.includes('Server running') || testProcess.includes('listening')) {
            console.log('✅ Server startup test passed\n');
        } else if (testProcess.includes('EADDRINUSE')) {
            console.log('⚠️  Port 5000 is already in use');
            console.log('   You may need to stop other services or change the port\n');
        } else if (testProcess.includes('MongoNetworkError')) {
            console.log('⚠️  MongoDB connection error detected');
            console.log('   Please ensure MongoDB is running or update MONGODB_URI\n');
        } else {
            console.log('⚠️  Server startup test completed with warnings\n');
        }
    } catch (error) {
        console.log('⚠️  Could not test server startup (this is often normal)\n');
    }
}

// Display next steps
function displayNextSteps() {
    console.log('🎉 Setup Complete!');
    console.log('==================\n');
    
    console.log('📝 Next Steps:');
    console.log('1. Ensure MongoDB is running:');
    console.log('   • Local: sudo systemctl start mongod (Linux) or brew services start mongodb-community (macOS)');
    console.log('   • Atlas: Update MONGODB_URI in .env with your Atlas connection string\n');
    
    console.log('2. Update environment variables in .env if needed:');
    console.log('   • MONGODB_URI (if using Atlas or different local setup)');
    console.log('   • PORT (if 5000 is already in use)');
    console.log('   • FRONTEND_URL (if frontend runs on different port)\n');
    
    console.log('3. Start the development server:');
    console.log('   npm run dev\n');
    
    console.log('4. Test the API:');
    console.log('   curl http://localhost:5000\n');
    
    console.log('📚 Additional Commands:');
    console.log('   npm start        - Start production server');
    console.log('   npm run dev      - Start development server (with auto-restart)');
    console.log('   npm test         - Run tests (when implemented)\n');
    
    console.log('🔧 Troubleshooting:');
    console.log('   - Check README.md for detailed instructions');
    console.log('   - Ensure MongoDB is running and accessible');
    console.log('   - Verify environment variables in .env');
    console.log('   - Check that port 5000 is available\n');
    
    console.log('📖 Documentation:');
    console.log('   - API endpoints: See README.md');
    console.log('   - Environment setup: See .env.example');
    console.log('   - Deployment guide: See README.md\n');
    
    console.log('✨ Happy coding!');
}

// Main setup function
function main() {
    try {
        checkNodeVersion();
        checkPackageJson();
        installDependencies();
        createEnvFile();
        
        // Add a small delay before testing connections
        setTimeout(() => {
            checkMongoConnection();
            testServerStartup();
            displayNextSteps();
        }, 1000);
        
    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Run setup
main();
