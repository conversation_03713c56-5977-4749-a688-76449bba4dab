.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #000;
  color: #CAFF33;
  font-size: 1.2rem;
}

.pageContainer {
  min-height: 88vh;
  display: flex;
  flex-direction: column;
  padding-top: 60px;
  font-family: Arial, sans-serif;
  background-color: #000;
  color: #FFF;
}

.profileContainer {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 0 2rem;
  width: 100%;
  flex: 1;
}

.profileHeader {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2rem;
  position: relative;
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: #CAFF33;
  }
}

.subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
}

.verificationBadge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: absolute;
  top: 0;
  right: 0;
  
  @media (max-width: 768px) {
    position: static;
    margin-top: 1rem;
  }
  
  span {
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
  }
}

.verified {
  background-color: rgba(0, 255, 0, 0.2);
  color: #00FF00;
}

.unverified {
  background-color: rgba(255, 0, 0, 0.2);
  color: #FF5555;
}

.verifyButton {
  padding: 0.3rem 0.6rem;
  background-color: #CAFF33;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #9CC026;
  }
}

.profileContent {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.personalInfo {
  flex: 1;
  background-color: #111;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h3 {
    font-size: 1.2rem;
    color: #CAFF33;
  }
}

.editButton {
  padding: 0.6rem 1.5rem;
  background-color: #CAFF33;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #9CC026;
  }
}

.editActions {
  display: flex;
  gap: 1rem;
}

.cancelButton {
  padding: 0.6rem 1.5rem;
  background-color: transparent;
  color: #FFF;
  border: 1px solid #333;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #333;
  }
}

.saveButton {
  padding: 0.6rem 1.5rem;
  background-color: #CAFF33;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #9CC026;
  }
}

.profileForm {
  width: 100%;
}

.formRow {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: 576px) {
    flex-direction: column;
    gap: 1rem;
  }
}

.formGroup {
  flex: 1;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
  }
  
  input, select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #333;
    border-radius: 4px;
    font-size: 1rem;
    background-color: #222;
    color: #FFF;
    
    &:focus {
      outline: none;
      border-color: #CAFF33;
    }
  }
  
  p {
    font-size: 1rem;
    color: #FFF;
    padding: 0.8rem 0;
  }
}

.selectInput {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 1em;
}

.securitySection {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #333;
}

.securityTitle {
  font-size: 1.1rem;
  color: #CAFF33;
  margin-bottom: 0.5rem;
}

.securityNote {
  font-size: 0.9rem;
  color: #999;
  margin-bottom: 1.5rem;
}

.errorMessage {
  color: #ff4d4d;
  background-color: rgba(255, 77, 77, 0.1);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.successMessage {
  color: #CAFF33;
  background-color: rgba(202, 255, 51, 0.1);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

