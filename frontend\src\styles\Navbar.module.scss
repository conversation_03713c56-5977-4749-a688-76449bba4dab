@use "sass:color";

$primary-color: #CAFF33;
$primary-dark: color.scale($primary-color, $lightness: -20%);

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.navbarLeft {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  
  img {
    height: 30px;
    margin-right: 0.5rem;
  }
}

.companyName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #CAFF33; 
}

.navbarCenter {
  flex: 1;
  display: flex;
  justify-content: center;
  
  @media (max-width: 768px) {
    display: none;
  }
}

.navLinks {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  
  li {
    margin: 0 1.5rem;
    
    a {
      color: #FFF;
      text-decoration: none;
      font-size: 1rem;
      font-weight: 500;
      transition: color 0.3s;
      padding: 0.5rem 0;
      position: relative;
      
      &:hover {
        color: #CAFF33;
      }
      
      &::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: 0;
        left: 0;
        background-color: #CAFF33;
        transition: width 0.3s;
      }
      
      &:hover::after {
        width: 100%;
      }
    }
    
    .verifyLink {
      color: #CAFF33;
      font-weight: 600;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 2px;
        bottom: 0;
        left: 0;
        background-color: #CAFF33;
      }
    }
  }
}

.navbarRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.loginBtn, .profileBtn {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1.5px solid $primary-color;
  color: $primary-color;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  
  &:hover {
    background-color: $primary-dark;
    border-color: $primary-dark;
    color: #000;
    text-decoration: none;
  }
}

.registerBtn, .logoutBtn {
  padding: 0.5rem 1rem;
  background-color: $primary-color;
  color: #000;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  
  &:hover {
    background-color: $primary-dark;
    text-decoration: none;
    color: #111;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
  }
  
  .navbarRight {
    gap: 0.5rem;
  }
  
  .loginBtn, .profileBtn, .registerBtn, .logoutBtn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}







