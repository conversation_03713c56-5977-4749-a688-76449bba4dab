# VerifID Backend Environment Variables
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration
# For local MongoDB:
MONGODB_URI=mongodb://localhost:27017/verifid

# For MongoDB Atlas (replace with your connection string):
# MONGODB_URI=mongodb+srv://username:<EMAIL>/verifid?retryWrites=true&w=majority

# For MongoDB with authentication:
# MONGODB_URI=***************************************************

# JWT Configuration
# Generate a strong secret key (32+ characters recommended)
# You can use: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# JWT token expiration time
# Examples: 1h, 24h, 7d, 30d
JWT_EXPIRES_IN=30d

# CORS Configuration
# Frontend URL for CORS (adjust based on your frontend setup)
FRONTEND_URL=http://localhost:5173

# Optional: Additional CORS origins (comma-separated)
# ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com

# Optional: Database Configuration
# DB_NAME=verifid
# DB_HOST=localhost
# DB_PORT=27017

# Optional: Logging Configuration
# LOG_LEVEL=info
# LOG_FILE=logs/app.log

# Optional: Rate Limiting
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# Optional: Session Configuration
# SESSION_SECRET=your-session-secret-here

# Production Environment Variables (uncomment for production)
# NODE_ENV=production
# PORT=80
# JWT_EXPIRES_IN=7d
# FRONTEND_URL=https://your-production-domain.com
