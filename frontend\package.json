{"name": "verifid", "private": true, "version": "0.0.0", "type": "module", "scripts": {"setup": "node setup.js", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.5.3", "react-router-dom": "^7.5.3", "sass": "^1.87.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}