# Core Flask dependencies
Flask==2.3.3
Flask-SocketIO==5.3.6
Flask-Cors==4.0.0
python-socketio==5.10.0
Werkzeug==2.3.7

# Environment and configuration
python-dotenv==1.0.0

# Image processing and computer vision
Pillow==10.0.0
opencv-python==4.8.0.74
numpy==1.24.3

# OCR (Optical Character Recognition)
pytesseract==0.3.10

# Face recognition and detection
dlib>=19.7
face_recognition==1.3.0

# Data visualization (used in some utilities)
matplotlib==3.7.2

# Async server support
gevent==23.9.1
gevent-websocket==0.10.1

# Note: Standard library modules (uuid, json, base64, io, os, time, tempfile,
# logging, traceback, datetime, string, re, random) are included with Python
# and don't need to be installed separately.

# Install face_recognition_models separately with:
# pip install git+https://github.com/ageitgey/face_recognition_models




