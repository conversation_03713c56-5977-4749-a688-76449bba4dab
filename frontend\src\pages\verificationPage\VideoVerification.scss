.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
}

.video-verification-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  margin-top: 60px; // Space for navbar
}

.video-verification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  
  h1 {
    margin-bottom: 2rem;
    color: #CAFF33;
    font-size: 2rem;
    text-align: center;
  }
  
  .video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    background-color: #111;
    
    video {
      width: 100%;
      height: auto;
      display: block;
      background-color: #000;
      
      &.completed {
        opacity: 0.7;
      }
    }
    
    .status-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      z-index: 10;
      padding: 20px;
      text-align: center;
      
      &.error {
        background-color: rgba(220, 53, 69, 0.8);
        
        i {
          font-size: 48px;
          margin-bottom: 20px;
        }
        
        p {
          margin-bottom: 20px;
          font-size: 18px;
        }
      }
      
      .button-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
        max-width: 300px;
        
        button {
          padding: 10px 15px;
          border: none;
          border-radius: 4px;
          background-color: #fff;
          color: #333;
          font-weight: bold;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          
          &:hover {
            background-color: #f8f9fa;
          }
          
          i {
            font-size: 16px;
          }
        }
      }
    }
  }
  
  .instruction-container {
    margin-top: 2rem;
    text-align: center;
    padding: 1.5rem;
    background-color: #111;
    border-radius: 8px;
    width: 100%;
    max-width: 640px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    
    h2 {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      color: #CAFF33;
    }
    
    p {
      color: #ccc;
      margin-bottom: 1rem;
    }
    
    .liveness-instructions {
      margin-top: 20px;
      padding: 15px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      
      p {
        color: #CAFF33;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      ol {
        text-align: left;
        margin-left: 20px;
        color: #fff;
      }
      
      li {
        margin: 8px 0;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Media queries for responsiveness
@media (max-width: 768px) {
  .video-verification-container {
    padding: 1rem;
    
    h1 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
    
    .instruction-container {
      padding: 1rem;
      
      h2 {
        font-size: 1.2rem;
      }
    }
  }
}


